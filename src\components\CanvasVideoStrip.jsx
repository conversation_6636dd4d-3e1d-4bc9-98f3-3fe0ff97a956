import { useEffect, useRef, useState, useCallback } from 'react'

// Polyfill for roundRect if not supported
const addRoundRectPolyfill = (ctx) => {
  if (!ctx.roundRect) {
    ctx.roundRect = function(x, y, width, height, radius) {
      this.beginPath()
      this.moveTo(x + radius, y)
      this.lineTo(x + width - radius, y)
      this.quadraticCurveTo(x + width, y, x + width, y + radius)
      this.lineTo(x + width, y + height - radius)
      this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
      this.lineTo(x + radius, y + height)
      this.quadraticCurveTo(x, y + height, x, y + height - radius)
      this.lineTo(x, y + radius)
      this.quadraticCurveTo(x, y, x + radius, y)
      this.closePath()
    }
  }
}

function CanvasVideoStrip() {
  const canvasRef = useRef(null)
  const animationRef = useRef(null)
  const videoRefs = useRef([])
  const lastFrameTime = useRef(0)
  const scrollPosition = useRef(0)
  const frameCount = useRef(0)
  const fpsCounter = useRef(0)
  const lastFpsUpdate = useRef(0)
  const [isLoaded, setIsLoaded] = useState(false)
  const [loadedVideos, setLoadedVideos] = useState(0)
  const [fps, setFps] = useState(0)



  // Local video data with optimized settings
  const videos = [
    {
      id: 1,
      title: "Creative Design",
      description: "UI/UX Solutions",
      videoUrl: "/vedio/6e6d2bb2360adbf20a0b1158be43dc90_720w.mp4",
      thumbnail: "https://via.placeholder.com/300x200/66FCF1/000000?text=Video+1"
    },
    {
      id: 2,
      title: "Web Development",
      description: "Modern Websites",
      videoUrl: "/vedio/6f95bf934d19df5e3d117dc5e32f4ed1_720w.mp4",
      thumbnail: "https://via.placeholder.com/300x200/45A29E/000000?text=Video+2"
    },
    {
      id: 3,
      title: "Mobile Apps",
      description: "iOS & Android",
      videoUrl: "/vedio/a95037b302539c241cbdfa6ed25be6cd.mp4",
      thumbnail: "https://via.placeholder.com/300x200/66FCF1/000000?text=Video+3"
    },
    {
      id: 4,
      title: "Brand Identity",
      description: "Logo & Graphics",
      videoUrl: "/vedio/af5dd8e6f3a902d2c21496d3ea8675aa.mp4",
      thumbnail: "https://via.placeholder.com/300x200/45A29E/000000?text=Video+4"
    }
  ]

  // Performance constants for 60fps optimization
  const FRAME_RATE = 60
  const FRAME_DURATION = 1000 / FRAME_RATE
  const SCROLL_SPEED = 2.5 // Pixels per frame for smooth scrolling
  const STRIP_WIDTH = 280
  const FRAME_WIDTH = 200
  const FRAME_HEIGHT = 120
  const FRAME_SPACING = 140

  // Dynamic strip height based on viewport (with extra coverage for rotation)
  const getStripHeight = () => {
    const canvas = canvasRef.current
    if (!canvas) return 1400
    // Calculate diagonal length to ensure full coverage when rotated 45 degrees
    const diagonal = Math.sqrt(canvas.width * canvas.width + canvas.height * canvas.height)
    return Math.max(diagonal * 1.2, 1400) // Ensure minimum height and extra coverage
  }

  // Optimized synchronization constants for smooth animation
  const TOTAL_LOOP_TIME = 15000 // 15 seconds for complete loop (faster, smoother)
  const SYNC_INTERVAL = TOTAL_LOOP_TIME / (videos.length * FRAME_SPACING)

  // Enhanced video initialization with fast preloading
  useEffect(() => {
    let loadCount = 0

    const handleVideoLoad = () => {
      loadCount++
      setLoadedVideos(loadCount)
      if (loadCount === videos.length) {
        // Ensure all videos have first frame ready before starting
        const prepareVideos = async () => {
          for (const video of videoRefs.current) {
            if (video) {
              // Set to first frame and wait for it to be ready
              video.currentTime = 0
              await new Promise(resolve => {
                const checkFrame = () => {
                  if (video.readyState >= 2) { // HAVE_CURRENT_DATA
                    resolve()
                  } else {
                    setTimeout(checkFrame, 10)
                  }
                }
                checkFrame()
              })

              // Start playing for smooth animation
              video.play().catch(() => {
                // Fallback: ensure at least first frame is loaded
                video.currentTime = 0.01
              })
            }
          }
          setIsLoaded(true)
        }

        prepareVideos()
      }
    }

    videos.forEach((video, index) => {
      const videoElement = document.createElement('video')
      // Set video properties for local videos (no CORS needed)
      videoElement.muted = true
      videoElement.loop = true
      videoElement.playsInline = true
      videoElement.preload = 'auto' // Fast preloading for local videos

      // Force immediate frame availability
      videoElement.setAttribute('webkit-playsinline', 'true')
      videoElement.setAttribute('playsinline', 'true')



      // Set src last to trigger loading
      videoElement.src = video.videoUrl

      // Force immediate loading attempt
      videoElement.load()

      // Multiple event listeners for better loading detection
      videoElement.addEventListener('loadeddata', handleVideoLoad)
      videoElement.addEventListener('canplaythrough', handleVideoLoad)
      videoElement.addEventListener('loadedmetadata', () => {
        // Ensure first frame is ready
        videoElement.currentTime = 0
        // Force load first frame
        setTimeout(() => {
          videoElement.currentTime = 0.01
        }, 50)
      })
      videoElement.addEventListener('error', () => {
        // Silent error handling for production
        handleVideoLoad() // Still count as loaded to prevent hanging
      })

      videoRefs.current[index] = videoElement
    })

    return () => {
      videoRefs.current.forEach(video => {
        if (video) {
          try {
            video.pause()
            video.removeEventListener('loadeddata', handleVideoLoad)
            video.removeEventListener('canplaythrough', handleVideoLoad)
            video.removeEventListener('error', handleVideoLoad)
            video.src = ''
          } catch (error) {
            // Silent cleanup
          }
        }
      })
    }
  }, [])

  // Optimized canvas drawing function with 60fps targeting
  const drawFrame = useCallback((currentTime) => {
    try {
      const canvas = canvasRef.current
      if (!canvas || !isLoaded) {
        animationRef.current = requestAnimationFrame(drawFrame)
        return
      }

      // Smooth frame rate limiting with better timing
      const deltaTime = currentTime - lastFrameTime.current
      if (deltaTime < FRAME_DURATION) {
        animationRef.current = requestAnimationFrame(drawFrame)
        return
      }
      lastFrameTime.current = currentTime - (deltaTime % FRAME_DURATION)

      // FPS monitoring
      frameCount.current++
      if (currentTime - lastFpsUpdate.current >= 1000) {
        setFps(frameCount.current)
        frameCount.current = 0
        lastFpsUpdate.current = currentTime
      }

      const ctx = canvas.getContext('2d')
      const { width, height } = canvas

      // Add roundRect polyfill if needed
      addRoundRectPolyfill(ctx)

      // Optimized canvas clearing for better performance
      ctx.fillStyle = '#000000'
      ctx.fillRect(0, 0, width, height)

      // Enable hardware acceleration hints
      ctx.imageSmoothingEnabled = true
      ctx.imageSmoothingQuality = 'high'

      // Save context for transformations
      ctx.save()

      // Position the film strip on the right side to avoid left content
      // Move to right side with better positioning for left content visibility
      ctx.translate(width - 100, height * 0.5)
      ctx.rotate(Math.PI / 4) // 45 degrees

      // Draw film strip components
      drawFilmStripBackground(ctx)
      drawScrollingVideos(ctx)
      drawFilmStripHoles(ctx)



      ctx.restore()

      // Update scroll position with synchronized timing
      const elapsedTime = currentTime - (animationRef.startTime || currentTime)
      if (!animationRef.startTime) {
        animationRef.startTime = currentTime
      }

      // Calculate synchronized scroll position based on dynamic strip height
      const stripHeight2 = getStripHeight()
      const videosNeeded = Math.ceil(stripHeight2 / FRAME_SPACING) + 2
      const totalHeight = videosNeeded * FRAME_SPACING
      const loopProgress = (elapsedTime % TOTAL_LOOP_TIME) / TOTAL_LOOP_TIME

      // Start with first frame visible (offset by half frame spacing)
      scrollPosition.current = (loopProgress * totalHeight) + (FRAME_SPACING / 2)

      // Ensure all videos stay in sync with scroll timing
      syncVideoPlayback(loopProgress)

      animationRef.current = requestAnimationFrame(drawFrame)
    } catch (error) {
      // Continue animation even if there's an error
      animationRef.current = requestAnimationFrame(drawFrame)
    }
  }, [isLoaded, videos.length])

  // Synchronized video playback with scroll timing
  const syncVideoPlayback = useCallback((loopProgress) => {
    if (!isLoaded) return

    // Calculate which video should be most prominent based on scroll position
    const currentVideoIndex = Math.floor(loopProgress * videos.length) % videos.length
    const nextVideoIndex = (currentVideoIndex + 1) % videos.length

    // Ensure all videos are playing for seamless transitions
    videoRefs.current.forEach((video, index) => {
      if (video && video.paused) {
        video.play().catch(console.warn)
      }

      // Sync video timing with scroll position for smooth transitions
      if (video && video.duration) {
        const videoProgress = (loopProgress * videos.length - index) % 1
        if (videoProgress >= 0 && videoProgress <= 1) {
          const targetTime = videoProgress * video.duration
          const timeDiff = Math.abs(video.currentTime - targetTime)

          // Only adjust if significantly out of sync (> 0.5 seconds)
          if (timeDiff > 0.5) {
            video.currentTime = targetTime
          }
        }
      }
    })
  }, [isLoaded, videos.length])

  // Enhanced film strip background with dark theme
  const drawFilmStripBackground = useCallback((ctx) => {
    const stripHeight = getStripHeight()

    // Create authentic film strip with dark theme
    const gradient = ctx.createLinearGradient(-STRIP_WIDTH/2, 0, STRIP_WIDTH/2, 0)
    gradient.addColorStop(0, 'rgba(0, 0, 0, 0.95)')
    gradient.addColorStop(0.08, 'rgba(15, 15, 15, 0.98)')
    gradient.addColorStop(0.15, 'rgba(25, 25, 25, 1)')
    gradient.addColorStop(0.85, 'rgba(25, 25, 25, 1)')
    gradient.addColorStop(0.92, 'rgba(15, 15, 15, 0.98)')
    gradient.addColorStop(1, 'rgba(0, 0, 0, 0.95)')

    ctx.fillStyle = gradient
    ctx.fillRect(-STRIP_WIDTH/2, -stripHeight/2, STRIP_WIDTH, stripHeight)

    // Enhanced film strip edges with cyan accent
    ctx.shadowColor = 'rgba(0, 0, 0, 0.9)'
    ctx.shadowBlur = 20
    ctx.shadowOffsetX = 8
    ctx.shadowOffsetY = 8
    ctx.strokeStyle = 'rgba(102, 252, 241, 0.3)'
    ctx.lineWidth = 2
    ctx.strokeRect(-STRIP_WIDTH/2, -stripHeight/2, STRIP_WIDTH, stripHeight)

    // Reset shadow
    ctx.shadowColor = 'transparent'
    ctx.shadowBlur = 0
    ctx.shadowOffsetX = 0
    ctx.shadowOffsetY = 0
  }, [])

  // Optimized scrolling videos with seamless bottom-to-top loop
  const drawScrollingVideos = useCallback((ctx) => {
    const stripHeight = getStripHeight()
    const totalHeight = videos.length * FRAME_SPACING

    // Create more video instances to fill the larger strip height
    const videosNeeded = Math.ceil(stripHeight / FRAME_SPACING) + 2
    const videoInstances = []

    for (let i = 0; i < videosNeeded; i++) {
      const videoIndex = i % videos.length
      videoInstances.push({ ...videos[videoIndex], instanceIndex: i, originalIndex: videoIndex })
    }

    videoInstances.forEach((video, instanceIndex) => {
      const videoElement = videoRefs.current[video.originalIndex]
      // Don't skip videos - always draw something, even if not ready

      // Calculate position for continuous bottom-to-top scrolling
      // Start with first frame visible by offsetting properly
      const baseY = (instanceIndex * FRAME_SPACING) - (stripHeight / 2) + FRAME_SPACING
      let scrolledY = baseY - scrollPosition.current

      // Create seamless loop by duplicating frames
      const positions = [scrolledY]
      if (scrolledY > stripHeight/2) {
        positions.push(scrolledY - videosNeeded * FRAME_SPACING)
      }
      if (scrolledY < -stripHeight/2) {
        positions.push(scrolledY + videosNeeded * FRAME_SPACING)
      }

      positions.forEach(yPos => {
        // Skip if outside visible area (optimization)
        if (yPos < -stripHeight/2 - FRAME_HEIGHT || yPos > stripHeight/2 + FRAME_HEIGHT) return

        const frameX = -FRAME_WIDTH/2
        const frameY = yPos - FRAME_HEIGHT/2

        ctx.save()

        // Dark film frame background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.98)'
        ctx.fillRect(frameX - 10, frameY - 10, FRAME_WIDTH + 20, FRAME_HEIGHT + 20)

        // Cyan accent border for dark theme
        ctx.strokeStyle = 'rgba(102, 252, 241, 0.5)'
        ctx.lineWidth = 2
        ctx.strokeRect(frameX - 10, frameY - 10, FRAME_WIDTH + 20, FRAME_HEIGHT + 20)

        // Draw video frame - ensure first frames are never empty
        try {
          if (videoElement && videoElement.readyState >= 2) {
            // Video has current data - draw frame
            ctx.drawImage(videoElement, frameX, frameY, FRAME_WIDTH, FRAME_HEIGHT)
          } else if (videoElement && videoElement.readyState >= 1) {
            // Video metadata loaded - force first frame and draw
            const originalTime = videoElement.currentTime
            videoElement.currentTime = 0.01 // Ensure we have a frame
            try {
              ctx.drawImage(videoElement, frameX, frameY, FRAME_WIDTH, FRAME_HEIGHT)
              videoElement.currentTime = originalTime // Restore time
            } catch (drawError) {
              // If can't draw video, show placeholder
              drawVideoPlaceholder(ctx, frameX, frameY, video.originalIndex)
            }
          } else if (videoElement) {
            // Video element exists but not ready
            drawVideoPlaceholder(ctx, frameX, frameY, video.originalIndex)
          } else {
            // No video element
            drawVideoPlaceholder(ctx, frameX, frameY, video.originalIndex)
          }
        } catch (error) {
          // Any error - show error placeholder
          drawVideoPlaceholder(ctx, frameX, frameY, video.originalIndex)
        }

        // Draw film perforations
        drawFilmPerforations(ctx, frameX, frameY, FRAME_WIDTH, FRAME_HEIGHT)

        // Optional frame number (disabled for cleaner look)
        const showFrameNumbers = false // Set to true if you want frame numbers
        if (showFrameNumbers) {
          ctx.fillStyle = 'rgba(102, 252, 241, 0.6)'
          ctx.font = '12px monospace'
          ctx.textAlign = 'center'
          ctx.fillText(`${video.originalIndex + 1}`, 0, yPos + FRAME_HEIGHT/2 + 20)
        }

        ctx.restore()
      })
    })
  }, [videos.length])

  // Draw minimal video placeholder
  const drawVideoPlaceholder = useCallback((ctx, frameX, frameY, videoIndex) => {
    // Dark background with subtle gradient
    const gradient = ctx.createLinearGradient(frameX, frameY, frameX, frameY + FRAME_HEIGHT)
    gradient.addColorStop(0, 'rgba(30, 30, 30, 0.9)')
    gradient.addColorStop(1, 'rgba(20, 20, 20, 0.95)')
    ctx.fillStyle = gradient
    ctx.fillRect(frameX, frameY, FRAME_WIDTH, FRAME_HEIGHT)

    // Optional frame number for placeholders (disabled for cleaner look)
    const showPlaceholderNumbers = false
    if (showPlaceholderNumbers) {
      ctx.fillStyle = 'rgba(102, 252, 241, 0.2)'
      ctx.font = '10px monospace'
      ctx.textAlign = 'center'
      ctx.fillText(`${videoIndex + 1}`, frameX + FRAME_WIDTH/2, frameY + FRAME_HEIGHT/2)
    }
  }, [])

  // Enhanced film perforations with dark theme
  const drawFilmPerforations = useCallback((ctx, x, y, width, height) => {
    const holeSize = 5
    const holeSpacing = 15
    const holes = Math.floor(height / holeSpacing)

    ctx.fillStyle = 'rgba(0, 0, 0, 0.95)'

    // Left perforations
    for (let i = 0; i < holes; i++) {
      const holeY = y + (i * holeSpacing) + holeSpacing/2
      ctx.beginPath()
      ctx.roundRect(x - 15, holeY - holeSize/2, 10, holeSize, 2)
      ctx.fill()
    }

    // Right perforations
    for (let i = 0; i < holes; i++) {
      const holeY = y + (i * holeSpacing) + holeSpacing/2
      ctx.beginPath()
      ctx.roundRect(x + width + 5, holeY - holeSize/2, 10, holeSize, 2)
      ctx.fill()
    }
  }, [])

  // Animated film strip holes with synchronized scrolling
  const drawFilmStripHoles = useCallback((ctx) => {
    const stripHeight = getStripHeight()
    const holeSize = 10
    const holeSpacing = 25
    const holesCount = Math.floor(stripHeight / holeSpacing) + 4 // Extra holes for coverage

    ctx.fillStyle = 'rgba(0, 0, 0, 0.95)'

    // Left side holes with scrolling animation
    for (let i = 0; i < holesCount; i++) {
      const y = (i * holeSpacing) - stripHeight/2 + 15 - (scrollPosition.current % holeSpacing)
      if (y > -stripHeight/2 && y < stripHeight/2) {
        ctx.beginPath()
        ctx.roundRect(-STRIP_WIDTH/2 + 10, y, 15, holeSize, 3)
        ctx.fill()
      }
    }

    // Right side holes with scrolling animation
    for (let i = 0; i < holesCount; i++) {
      const y = (i * holeSpacing) - stripHeight/2 + 15 - (scrollPosition.current % holeSpacing)
      if (y > -stripHeight/2 && y < stripHeight/2) {
        ctx.beginPath()
        ctx.roundRect(STRIP_WIDTH/2 - 25, y, 15, holeSize, 3)
        ctx.fill()
      }
    }
  }, [])



  // Initialize canvas and start animation
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    // Set canvas size
    const updateCanvasSize = () => {
      canvas.width = window.innerWidth
      canvas.height = window.innerHeight
    }

    updateCanvasSize()

    // Start animation loop only after videos are loaded
    if (isLoaded) {
      // Small delay to ensure all videos have first frame ready
      setTimeout(() => {
        animationRef.current = requestAnimationFrame(drawFrame)
      }, 100)
    }

    // Handle resize
    const handleResize = () => {
      updateCanvasSize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
      window.removeEventListener('resize', handleResize)
    }
  }, [drawFrame, isLoaded])

  // Minimal loading indicator
  const LoadingIndicator = () => (
    <div className="fixed top-4 right-4 z-30 bg-black bg-opacity-60 rounded-lg p-3">
      <div className="flex items-center space-x-2">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-cyan-400"></div>
        <span className="text-xs font-mono text-cyan-400">
          {Math.round((loadedVideos / videos.length) * 100)}%
        </span>
      </div>
    </div>
  )

  // Optional performance monitor (hidden by default for production)
  const PerformanceMonitor = () => {
    // Only show in development or when explicitly enabled
    const showMonitor = false // Disabled for clean production look

    if (!showMonitor) return null

    const canvas = canvasRef.current
    const stripHeight = canvas ? getStripHeight() : 0
    const videosNeeded = canvas ? Math.ceil(stripHeight / FRAME_SPACING) + 2 : 0

    return (
      <div className="fixed top-4 left-4 z-30 bg-black bg-opacity-80 rounded-lg p-2 text-cyan-400 text-xs font-mono">
        <div>FPS: {fps}</div>
        <div className={fps >= 55 ? 'text-green-400' : fps >= 30 ? 'text-yellow-400' : 'text-red-400'}>
          {fps >= 55 ? '✓ Smooth' : fps >= 30 ? '⚠ OK' : '✗ Slow'}
        </div>
      </div>
    )
  }

  return (
    <>
      {!isLoaded && <LoadingIndicator />}
      {isLoaded && <PerformanceMonitor />}
      <canvas
        ref={canvasRef}
        className="fixed top-0 left-0 w-full h-full z-0 pointer-events-none scrolling-video-canvas gpu-accelerated smooth-60fps"
        style={{
          background: 'transparent',
          mixBlendMode: 'normal',
          imageRendering: 'crisp-edges'
        }}
      />
    </>
  )
}

export default CanvasVideoStrip
