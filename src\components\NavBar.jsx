import { useState } from 'react'

function NavBar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const navItems = [
    { name: 'Home', href: '#home', action: () => window.scrollTo({ top: 0, behavior: 'smooth' }) },
    { name: 'About', href: '#about', action: () => window.scrollToAbout && window.scrollToAbout() },
    { name: 'Services', href: '#services' },
    { name: 'Portfolio', href: '#portfolio' },
    { name: 'Contact', href: '#contact' }
  ]

  const handleNavClick = (item, e) => {
    e.preventDefault()
    if (item.action) {
      item.action()
    }
  }

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 backdrop-blur-lg border-b shadow-lg"
         style={{
           backgroundColor: 'rgba(0, 0, 0, 0.9)',
           borderColor: 'rgba(102, 252, 241, 0.2)',
           boxShadow: '0 4px 20px rgba(102, 252, 241, 0.1)'
         }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20">
          {/* Logo */}
          <div className="flex-shrink-0">
            <a href="#" className="text-2xl font-bold transition-all duration-300 hover:scale-105"
               style={{
                 color: '#66FCF1',
                 textShadow: '0 0 10px rgba(102, 252, 241, 0.5)',
                 fontFamily: 'monospace'
               }}>
              BERT
            </a>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-center space-x-1">
              {navItems.map((item) => (
                <a
                  key={item.name}
                  href={item.href}
                  className="px-4 py-2 text-sm font-medium transition-all duration-300 relative group rounded-lg cursor-pointer"
                  style={{color: '#C5C6C7'}}
                  onClick={(e) => handleNavClick(item, e)}
                  onMouseEnter={(e) => {
                    e.target.style.color = '#66FCF1'
                    e.target.style.backgroundColor = 'rgba(102, 252, 241, 0.1)'
                    e.target.style.transform = 'translateY(-2px)'
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.color = '#C5C6C7'
                    e.target.style.backgroundColor = 'transparent'
                    e.target.style.transform = 'translateY(0)'
                  }}
                >
                  {item.name}
                  <span className="absolute bottom-1 left-1/2 w-0 h-0.5 transition-all duration-300 group-hover:w-3/4 transform -translate-x-1/2"
                        style={{backgroundColor: '#66FCF1', boxShadow: '0 0 5px rgba(102, 252, 241, 0.8)'}}></span>
                </a>
              ))}
            </div>
          </div>

          {/* CTA Button */}
          <div className="hidden md:block">
            <button className="px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 border-2 backdrop-blur-sm relative overflow-hidden group"
                    style={{
                      backgroundColor: 'transparent',
                      color: '#66FCF1',
                      borderColor: '#66FCF1',
                      boxShadow: '0 0 15px rgba(102, 252, 241, 0.3)'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.backgroundColor = '#66FCF1'
                      e.target.style.color = '#000000'
                      e.target.style.transform = 'translateY(-2px)'
                      e.target.style.boxShadow = '0 5px 25px rgba(102, 252, 241, 0.5)'
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.backgroundColor = 'transparent'
                      e.target.style.color = '#66FCF1'
                      e.target.style.transform = 'translateY(0)'
                      e.target.style.boxShadow = '0 0 15px rgba(102, 252, 241, 0.3)'
                    }}>
              <span className="relative z-10">Get Started</span>
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="focus:outline-none transition-all duration-300 p-2 rounded-lg"
              style={{
                color: '#C5C6C7',
                border: '1px solid rgba(102, 252, 241, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.target.style.color = '#66FCF1'
                e.target.style.backgroundColor = 'rgba(102, 252, 241, 0.1)'
                e.target.style.borderColor = '#66FCF1'
              }}
              onMouseLeave={(e) => {
                e.target.style.color = '#C5C6C7'
                e.target.style.backgroundColor = 'transparent'
                e.target.style.borderColor = 'rgba(102, 252, 241, 0.3)'
              }}
            >
              <svg className="h-6 w-6 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor"
                   style={{transform: isMenuOpen ? 'rotate(90deg)' : 'rotate(0deg)'}}>
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {isMenuOpen && (
        <div className="md:hidden backdrop-blur-lg border-t animate-slideDown"
             style={{
               backgroundColor: 'rgba(0, 0, 0, 0.95)',
               borderColor: 'rgba(102, 252, 241, 0.2)',
               boxShadow: '0 4px 20px rgba(102, 252, 241, 0.1)'
             }}>
          <div className="px-4 pt-4 pb-6 space-y-2">
            {navItems.map((item, index) => (
              <a
                key={item.name}
                href={item.href}
                className="block px-4 py-3 text-base font-medium transition-all duration-300 rounded-lg cursor-pointer"
                style={{
                  color: '#C5C6C7',
                  animationDelay: `${index * 0.1}s`
                }}
                onMouseEnter={(e) => {
                  e.target.style.color = '#66FCF1'
                  e.target.style.backgroundColor = 'rgba(102, 252, 241, 0.1)'
                  e.target.style.transform = 'translateX(10px)'
                }}
                onMouseLeave={(e) => {
                  e.target.style.color = '#C5C6C7'
                  e.target.style.backgroundColor = 'transparent'
                  e.target.style.transform = 'translateX(0)'
                }}
                onClick={(e) => {
                  handleNavClick(item, e)
                  setIsMenuOpen(false)
                }}
              >
                {item.name}
              </a>
            ))}
            <div className="pt-4">
              <button className="w-full px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-300 border-2"
                      style={{
                        backgroundColor: 'transparent',
                        color: '#66FCF1',
                        borderColor: '#66FCF1',
                        boxShadow: '0 0 15px rgba(102, 252, 241, 0.3)'
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.backgroundColor = '#66FCF1'
                        e.target.style.color = '#000000'
                        e.target.style.boxShadow = '0 5px 25px rgba(102, 252, 241, 0.5)'
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.backgroundColor = 'transparent'
                        e.target.style.color = '#66FCF1'
                        e.target.style.boxShadow = '0 0 15px rgba(102, 252, 241, 0.3)'
                      }}>
                Get Started
              </button>
            </div>
          </div>
        </div>
      )}
    </nav>
  )
}

export default NavBar
