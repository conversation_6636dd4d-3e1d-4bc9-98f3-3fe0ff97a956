import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

// Global function to scroll to about section
window.scrollToAbout = () => {
  const targetScroll = window.innerHeight * 2.5 // Scroll to about section
  window.scrollTo({
    top: targetScroll,
    behavior: 'smooth'
  })
}

function AboutCard() {
  const cardRef = useRef(null)
  const contentRef = useRef(null)
  const aboutTextRef = useRef(null)

  useEffect(() => {
    const card = cardRef.current
    const content = contentRef.current
    const aboutText = aboutTextRef.current
    const hero = document.querySelector('[data-hero-section]')

    if (!card || !content || !aboutText) return

    // Set initial state - card hidden at bottom
    gsap.set(card, {
      y: window.innerHeight + 100, // Start below viewport
      scale: 0.3,
      opacity: 0,
      left: '50%',
      top: '0',
      transform: 'translateX(-50%)'
    })

    gsap.set(content, {
      opacity: 0,
      y: 50
    })

    gsap.set(aboutText, {
      opacity: 1
    })

    // Create scroll trigger animation
    ScrollTrigger.create({
      trigger: document.body,
      start: 'top top',
      end: 'bottom bottom',
      scrub: 1,
      onUpdate: (self) => {
        const progress = self.progress

        // Phase 1: Card emerges from bottom (0-0.3)
        if (progress <= 0.3) {
          const phase1Progress = progress / 0.3
          const startY = window.innerHeight + 100
          const endY = window.innerHeight * 0.4 // 40% from top
          const currentY = startY - (phase1Progress * (startY - endY))

          gsap.set(card, {
            y: currentY,
            scale: 0.3 + (phase1Progress * 0.2),
            opacity: phase1Progress,
            width: '300px',
            height: '200px',
            borderRadius: '12px',
            left: '50%',
            transform: 'translateX(-50%)'
          })
        }
        // Phase 2: Card moves to center and grows (0.3-0.6)
        else if (progress <= 0.6) {
          const phase2Progress = (progress - 0.3) / 0.3
          const startY = window.innerHeight * 0.4
          const endY = window.innerHeight * 0.2 // Move to 20% from top (more centered)
          const currentY = startY - (phase2Progress * (startY - endY))

          gsap.set(card, {
            y: currentY,
            scale: 0.5 + (phase2Progress * 0.3),
            opacity: 1,
            width: `${300 + (phase2Progress * 200)}px`,
            height: `${200 + (phase2Progress * 150)}px`,
            borderRadius: `${12 - (phase2Progress * 6)}px`,
            left: '50%',
            transform: 'translateX(-50%)'
          })

          // Start hiding "About" text in phase 2
          gsap.set(aboutText, {
            opacity: 1 - (phase2Progress * 0.5) // Start fading at 50% of phase 2
          })
        }
        // Phase 3: Card expands to full screen (0.6-1.0)
        else {
          const phase3Progress = (progress - 0.6) / 0.4

          gsap.set(card, {
            y: 0,
            scale: 1,
            opacity: 1,
            width: '100vw',
            height: '100vh',
            borderRadius: '0px',
            left: '0',
            transform: 'translateX(0)',
            backgroundColor: '#000000' // Solid black background when fully triggered
          })

          // Hide "About" text completely
          gsap.set(aboutText, {
            opacity: 0
          })

          // Show content
          gsap.set(content, {
            opacity: phase3Progress,
            y: 50 - (phase3Progress * 50)
          })

          // Hide hero section
          if (hero) {
            gsap.set(hero, {
              opacity: 1 - (phase3Progress * 0.8)
            })
          }
        }
      }
    })

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <>
      {/* Spacer to enable scrolling */}
      <div style={{ height: '400vh' }} />

      {/* About Card */}
      <div
        ref={cardRef}
        className="fixed overflow-hidden"
        style={{
          backgroundColor: 'rgba(31, 41, 55, 0.95)',
          border: '2px solid #66FCF1',
          backdropFilter: 'blur(10px)',
          zIndex: 20,
          width: '300px',
          height: '200px',
          borderRadius: '12px',
          left: '50%',
          top: '0',
          transform: 'translateX(-50%)'
        }}
      >
        {/* Small card state - just "About" text */}
        <div ref={aboutTextRef} className="absolute inset-0 flex items-center justify-center">
          <h2
            className="text-2xl font-bold"
            style={{ color: '#66FCF1' }}
          >
            About
          </h2>
        </div>

        {/* Full screen content */}
        <div
          ref={contentRef}
          className="absolute inset-0 p-8 md:p-16 flex flex-col justify-center"
          style={{ opacity: 0 }}
        >
          {/* About content */}
          <div className="max-w-4xl">
            <h2 className="text-4xl md:text-6xl font-bold mb-8" style={{ color: '#66FCF1' }}>
              About Us
            </h2>
            <p className="text-lg md:text-xl mb-6 leading-relaxed" style={{ color: '#C5C6C7' }}>
              We are a creative studio dedicated to crafting exceptional digital experiences.
              Our team combines innovative design, cutting-edge development, and strategic thinking
              to deliver results that exceed expectations.
            </p>
            <p className="text-lg md:text-xl leading-relaxed" style={{ color: '#C5C6C7' }}>
              From concept to completion, we work closely with our clients to bring their vision to life
              through thoughtful design and meticulous attention to detail.
            </p>
          </div>

          {/* Spline 3D Viewer in right corner */}
          <div className="absolute top-48 right-8 w-[40rem] h-[40rem] md:w-[48rem] md:h-[48rem]">
            <spline-viewer url="https://prod.spline.design/y2QLDe7VWlePnb9X/scene.splinecode"></spline-viewer>
          </div>
        </div>
      </div>
    </>
  )
}

export default AboutCard