import { useEffect, useRef, useState } from 'react';

const SplineBackground = () => {
  const splineRef = useRef(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const loadSpline = async () => {
      try {
        console.log('Attempting to load Spline scene...');

        // Wait for spline-viewer to be available
        if (typeof window !== 'undefined' && window.customElements) {
          // Create spline viewer element
          const splineViewer = document.createElement('spline-viewer');
          splineViewer.setAttribute('url', 'https://prod.spline.design/perCAIenhEw2tEZc/scene.splinecode');
          splineViewer.style.width = '100%';
          splineViewer.style.height = '100%';

          // Add event listeners
          splineViewer.addEventListener('load', () => {
            console.log('Spline scene loaded successfully');
            setIsLoaded(true);
          });

          splineViewer.addEventListener('error', (error) => {
            console.error('Spline scene failed to load:', error);
            setHasError(true);
          });

          // Clear any existing content and append
          if (splineRef.current) {
            splineRef.current.innerHTML = '';
            splineRef.current.appendChild(splineViewer);
          }
        } else {
          console.error('Spline viewer not available');
          setHasError(true);
        }
      } catch (error) {
        console.error('Error loading Spline scene:', error);
        setHasError(true);
      }
    };

    // Small delay to ensure the script is loaded
    const timer = setTimeout(loadSpline, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div
      ref={splineRef}
      className="fixed inset-0 w-full h-full opacity-80 pointer-events-none z-0"
      style={{
        background: hasError ? 'linear-gradient(45deg, #1a1a1a 25%, transparent 25%), linear-gradient(-45deg, #1a1a1a 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #1a1a1a 75%), linear-gradient(-45deg, transparent 75%, #1a1a1a 75%)' : 'transparent',
        backgroundSize: hasError ? '20px 20px' : 'auto',
        backgroundPosition: hasError ? '0 0, 0 10px, 10px -10px, -10px 0px' : 'auto',
        overflow: 'hidden'
      }}
    >
      {hasError && (
        <div className="flex items-center justify-center h-full text-gray-500 text-sm">
          <div className="text-center">
            <div>3D Background Loading...</div>
            <div className="text-xs mt-2">Fallback pattern active</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SplineBackground;
