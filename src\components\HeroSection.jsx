function HeroSection() {
  return (
    <div className="fixed inset-0 flex items-center justify-start pl-8 md:pl-16 lg:pl-24" style={{zIndex: 10, paddingTop: '80px'}} data-hero-section>
      <div className="text-left max-w-3xl">
        <h1 className="text-6xl md:text-7xl font-bold mb-6" style={{color: '#66FCF1'}}>
          CREATIVE
          <br />
          <span className="text-4xl md:text-5xl" style={{color: '#C5C6C7'}}>STUDIO</span>
        </h1>
        <p className="text-lg md:text-xl mb-8 leading-relaxed" style={{color: '#C5C6C7'}}>
          We craft exceptional digital experiences through innovative design,
          cutting-edge development, and strategic thinking that drives results.
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <button className="px-8 py-2 border-2 text-lg font-medium transition-all duration-300 hover:bg-opacity-20"
                  style={{
                    borderColor: '#66FCF1',
                    color: '#66FCF1',
                    backgroundColor: 'transparent'
                  }}
                  onClick={() => alert('View Our Work clicked!')}
                  onMouseEnter={(e) => e.target.style.backgroundColor = 'rgba(102, 252, 241, 0.1)'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}>
            VIEW OUR WORK
          </button>
          <button className="px-8 py-2 text-lg font-medium transition-all duration-300"
                  style={{
                    backgroundColor: '#66FCF1',
                    color: '#000000'
                  }}
                  onClick={() => alert('Get Started clicked!')}
                  onMouseEnter={(e) => {
                    e.target.style.backgroundColor = '#45A29E'
                    e.target.style.transform = 'translateY(-2px)'
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.backgroundColor = '#66FCF1'
                    e.target.style.transform = 'translateY(0)'
                  }}>
            GET STARTED
          </button>
        </div>
      </div>
    </div>
  )
}

export default HeroSection
