import { useState, useRef, useEffect } from 'react'

function VideoStrip() {
  const [activeVideo, setActiveVideo] = useState(0)
  const videoRefs = useRef([])

  // Sample video data - replace with your actual video URLs
  const videos = [
    {
      id: 1,
      title: "Project Alpha",
      description: "Creative Design Solution",
      // For demo, using placeholder. Replace with actual video URLs
      videoUrl: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
      thumbnail: "https://via.placeholder.com/300x200/66FCF1/000000?text=Video+1"
    },
    {
      id: 2,
      title: "Project Beta",
      description: "Web Development",
      videoUrl: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
      thumbnail: "https://via.placeholder.com/300x200/45A29E/000000?text=Video+2"
    },
    {
      id: 3,
      title: "Project Gamma",
      description: "Mobile App Design",
      videoUrl: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4",
      thumbnail: "https://via.placeholder.com/300x200/66FCF1/000000?text=Video+3"
    },
    {
      id: 4,
      title: "Project Delta",
      description: "Brand Identity",
      videoUrl: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4",
      thumbnail: "https://via.placeholder.com/300x200/45A29E/000000?text=Video+4"
    },
    {
      id: 5,
      title: "Project Epsilon",
      description: "UI/UX Design",
      videoUrl: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4",
      thumbnail: "https://via.placeholder.com/300x200/66FCF1/000000?text=Video+5"
    }
  ]

  const handleVideoClick = (index) => {
    // Pause all videos
    videoRefs.current.forEach((video, i) => {
      if (video && i !== index) {
        video.pause()
      }
    })
    
    // Play selected video
    if (videoRefs.current[index]) {
      videoRefs.current[index].play()
    }
    
    setActiveVideo(index)
  }

  useEffect(() => {
    // Auto-play first video on mount
    if (videoRefs.current[0]) {
      videoRefs.current[0].play()
    }
  }, [])

  return (
    <div className="fixed right-0 top-0 h-screen w-96 z-20 pointer-events-auto overflow-hidden">
      {/* Ambient Glow Effect */}
      <div className="absolute inset-0 bg-gradient-radial from-cyan-500/10 via-transparent to-transparent opacity-50 animate-pulse-slow"></div>

      {/* Rotated Film Strip Container */}
      <div className="absolute inset-0 transform rotate-45 origin-center video-strip-container" style={{
        width: '180%',
        height: '180%',
        top: '-40%',
        right: '-40%'
      }}>
        {/* Enhanced Film Strip Background */}
        <div className="absolute inset-0 bg-gradient-to-l from-black via-gray-900 to-gray-800 opacity-95 backdrop-blur-sm"></div>

        {/* Film Strip Shadow */}
        <div className="absolute inset-0 shadow-2xl" style={{
          boxShadow: 'inset 0 0 50px rgba(0,0,0,0.8), 0 0 100px rgba(102, 252, 241, 0.1)'
        }}></div>

        {/* Enhanced Film Strip Holes - Left Side */}
        <div className="absolute left-0 top-0 h-full w-6 flex flex-col justify-around film-strip-holes-left">
          {Array.from({ length: 40 }, (_, i) => (
            <div
              key={i}
              className="w-4 h-5 bg-black rounded-sm border border-cyan-500/30 shadow-inner"
              style={{
                marginLeft: '3px',
                boxShadow: 'inset 0 0 5px rgba(0,0,0,0.8), 0 0 3px rgba(102, 252, 241, 0.2)'
              }}
            ></div>
          ))}
        </div>

        {/* Enhanced Film Strip Holes - Right Side */}
        <div className="absolute right-0 top-0 h-full w-6 flex flex-col justify-around film-strip-holes-right">
          {Array.from({ length: 40 }, (_, i) => (
            <div
              key={i}
              className="w-4 h-5 bg-black rounded-sm border border-cyan-500/30 shadow-inner"
              style={{
                marginRight: '3px',
                boxShadow: 'inset 0 0 5px rgba(0,0,0,0.8), 0 0 3px rgba(102, 252, 241, 0.2)'
              }}
            ></div>
          ))}
        </div>

        {/* Film Strip Center Line */}
        <div className="absolute left-1/2 top-0 h-full w-0.5 bg-gradient-to-b from-cyan-500/20 via-cyan-500/10 to-cyan-500/20 transform -translate-x-1/2"></div>

        {/* Continuous Scrolling Video Strip */}
        <div className="relative h-full ml-6 mr-6 py-10">
          <div className="video-strip-scroll">
            {/* First set of videos */}
            <div className="space-y-6">
              {videos.map((video, index) => (
                <VideoFrame
                  key={`first-${video.id}`}
                  video={video}
                  index={index}
                  activeVideo={activeVideo}
                  videoRefs={videoRefs}
                  handleVideoClick={handleVideoClick}
                />
              ))}
            </div>

            {/* Duplicate set for continuous loop */}
            <div className="space-y-6 mt-6">
              {videos.map((video, index) => (
                <VideoFrame
                  key={`second-${video.id}`}
                  video={video}
                  index={index + videos.length}
                  activeVideo={activeVideo}
                  videoRefs={videoRefs}
                  handleVideoClick={handleVideoClick}
                />
              ))}
            </div>

            {/* Third set for smoother loop */}
            <div className="space-y-8 mt-8">
              {videos.map((video, index) => (
                <VideoFrame
                  key={`third-${video.id}`}
                  video={video}
                  index={index + videos.length * 2}
                  activeVideo={activeVideo}
                  videoRefs={videoRefs}
                  handleVideoClick={handleVideoClick}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Film Strip Gradient Overlays */}
        <div className="absolute top-0 left-0 right-0 h-32 bg-gradient-to-b from-black via-black/50 to-transparent pointer-events-none"></div>
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black via-black/50 to-transparent pointer-events-none"></div>
      </div>

      {/* Floating Particles Effect */}
      <div className="absolute inset-0 pointer-events-none">
        {Array.from({ length: 15 }, (_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-cyan-400/30 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          ></div>
        ))}
      </div>
    </div>
  )
}

// Enhanced VideoFrame Component
function VideoFrame({ video, index, activeVideo, videoRefs, handleVideoClick }) {
  return (
    <div
      className={`relative group cursor-pointer transition-all duration-500 video-frame-enhanced ${
        activeVideo === index ? 'scale-110 z-20' : 'scale-100 hover:scale-105 hover:z-10'
      }`}
      onClick={() => handleVideoClick(index)}
      style={{
        filter: activeVideo === index ? 'brightness(1.1) contrast(1.1)' : 'brightness(0.9)',
        transform: activeVideo === index ? 'translateX(-10px) scale(1.1)' : undefined
      }}
    >
      {/* Enhanced Video Frame */}
      <div className="relative bg-black rounded-xl overflow-hidden border-3 transition-all duration-500 backdrop-blur-sm"
           style={{
             borderColor: activeVideo === index ? '#66FCF1' : 'rgba(102, 252, 241, 0.4)',
             boxShadow: activeVideo === index
               ? '0 0 30px rgba(102, 252, 241, 0.8), 0 0 60px rgba(102, 252, 241, 0.4), inset 0 0 20px rgba(102, 252, 241, 0.1)'
               : '0 0 15px rgba(102, 252, 241, 0.3), inset 0 0 10px rgba(0,0,0,0.5)'
           }}>

        {/* Enhanced Video Container */}
        <div className="video-container-enhanced">
          <video
            ref={(el) => (videoRefs.current[index] = el)}
            className="w-full h-36 object-cover transition-all duration-500"
            muted
            loop
            playsInline
            poster={video.thumbnail}
            style={{
              filter: activeVideo === index ? 'brightness(1.1) contrast(1.1) saturate(1.2)' : 'brightness(0.8) contrast(0.9)'
            }}
          >
            <source src={video.videoUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </div>

        {/* Enhanced Video Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
          <div className="absolute bottom-3 left-3 right-3">
            <h3 className="text-sm font-bold text-cyan-300 mb-1 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
              {video.title}
            </h3>
            <p className="text-xs text-gray-300 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300 delay-100">
              {video.description}
            </p>
          </div>

          {/* Scan Line Effect */}
          <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-cyan-400 to-transparent opacity-70 animate-scan-line"></div>
        </div>

        {/* Enhanced Play Indicator */}
        {activeVideo === index && (
          <div className="absolute top-3 right-3 flex items-center space-x-2">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
            <div className="w-2 h-2 bg-red-300 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            <span className="text-xs text-red-400 font-mono">REC</span>
          </div>
        )}

        {/* Video Progress Bar */}
        {activeVideo === index && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/50">
            <div className="h-full bg-gradient-to-r from-cyan-500 to-cyan-300 animate-progress"></div>
          </div>
        )}

        {/* Film Strip Perforations */}
        <div className="absolute left-0 top-0 h-full w-1 flex flex-col justify-around">
          {Array.from({ length: 8 }, (_, i) => (
            <div key={i} className="w-1 h-1 bg-gray-800 rounded-full"></div>
          ))}
        </div>
        <div className="absolute right-0 top-0 h-full w-1 flex flex-col justify-around">
          {Array.from({ length: 8 }, (_, i) => (
            <div key={i} className="w-1 h-1 bg-gray-800 rounded-full"></div>
          ))}
        </div>
      </div>

      {/* Frame Number */}
      <div className="absolute -left-4 top-1/2 transform -translate-y-1/2 rotate-45">
        <span className="text-xs font-mono text-gray-500">
          {String((index % 5) + 1).padStart(2, '0')}
        </span>
      </div>
    </div>
  )
}

export default VideoStrip
