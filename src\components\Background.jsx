function Background({ children }) {
  return (
    <div className="min-h-screen bg-black flex items-center justify-start relative overflow-hidden">
      {/* Enhanced Dark Background Gradient */}
      <div className="absolute inset-0 bg-gradient-radial from-gray-900 via-black to-black opacity-90"></div>

      {/* Subtle Dark Overlay for Video Integration */}
      <div className="absolute inset-0 dark-video-overlay pointer-events-none opacity-30"></div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

export default Background
