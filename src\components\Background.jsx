import { useEffect, useState, useRef } from 'react'

function Background({ children }) {
  const [splineLoaded, setSplineLoaded] = useState(false)
  const [hasError, setHasError] = useState(false)
  const splineRef = useRef(null)

  useEffect(() => {
    // Check if spline-viewer is available and wait for it to load
    const checkSplineViewer = () => {
      try {
        if (typeof customElements !== 'undefined' && customElements.get('spline-viewer')) {
          setSplineLoaded(true)
        } else {
          // Wait a bit more for the script to load
          setTimeout(checkSplineViewer, 200)
        }
      } catch (error) {
        console.warn('Spline viewer not available:', error)
        setHasError(true)
      }
    }

    // Add a delay to ensure the script has time to load
    setTimeout(checkSplineViewer, 500)
  }, [])

  // Error handler for spline viewer
  useEffect(() => {
    if (splineRef.current) {
      const handleError = (event) => {
        console.warn('Spline viewer error:', event)
        setHasError(true)
      }

      splineRef.current.addEventListener('error', handleError)

      return () => {
        if (splineRef.current) {
          splineRef.current.removeEventListener('error', handleError)
        }
      }
    }
  }, [splineLoaded])

  return (
    <div className="min-h-screen bg-black flex items-center justify-start relative overflow-hidden">
      {/* Enhanced Dark Background Gradient */}
      <div className="absolute inset-0 bg-gradient-radial from-gray-900 via-black to-black opacity-90"></div>

      {/* Spline 3D Background Scene */}
      {splineLoaded && !hasError && (
        <div className="absolute inset-0 w-full h-full opacity-40 pointer-events-none" style={{ zIndex: 1 }}>
          <spline-viewer
            ref={splineRef}
            url="https://prod.spline.design/perCAIenhEw2tEZc/scene.splinecode"
            style={{ width: '100%', height: '100%' }}
          ></spline-viewer>
        </div>
      )}

      {/* Fallback animated background if Spline fails */}
      {(!splineLoaded || hasError) && (
        <div className="absolute inset-0 w-full h-full opacity-20 pointer-events-none" style={{ zIndex: 1 }}>
          <div className="absolute inset-0 bg-gradient-to-br from-cyan-900/20 via-transparent to-blue-900/20 animate-pulse"></div>
        </div>
      )}

      {/* Subtle Dark Overlay for Video Integration */}
      <div className="absolute inset-0 dark-video-overlay pointer-events-none opacity-30" style={{ zIndex: 2 }}></div>

      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

export default Background
