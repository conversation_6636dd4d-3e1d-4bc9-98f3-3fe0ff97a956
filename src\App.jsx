import './index.css'
import NavBar from './components/NavBar'
import CanvasVideoStrip from './components/CanvasVideoStrip'
import ErrorBoundary from './components/ErrorBoundary'
import HeroSection from './components/HeroSection'
import AboutCard from './components/AboutCard'
import SplineBackground from './components/SplineBackground'

function App() {
  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-black relative overflow-hidden">
        {/* Spline 3D Background */}
        <SplineBackground />

        {/* Dark overlay for better content visibility */}
        <div className="fixed inset-0 bg-black/20 z-10"></div>

        {/* App Content */}
        <div className="relative z-20">
          <NavBar />
          <CanvasVideoStrip />
          <HeroSection />
          <AboutCard />
        </div>
      </div>
    </ErrorBoundary>
  )
}

export default App
